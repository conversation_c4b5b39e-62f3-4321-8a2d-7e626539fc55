import TurndownService from 'turndown';
import { marked } from 'marked';

// Recreate our solution
const turndownService = new TurndownService({
  headingStyle: 'atx',
  codeBlockStyle: 'fenced', 
  fence: '```',
  bulletListMarker: '-',
  linkStyle: 'inlined',
  preformattedCode: true,
});

// Add our custom rule
turndownService.addRule('preserveIndentation', {
  filter: (node) => {
    return (
      node.nodeType === 3 && // Node.TEXT_NODE
      node.textContent &&
      /^(\s|&nbsp;|\u00A0|&#160;|&#xa0;)+/.test(node.textContent)
    );
  },
  replacement: (content) => {
    return content.replace(/^(\s|&nbsp;|\u00A0|&#160;|&#xa0;)+/gi, (match) => {
      let spaceCount = 0;
      spaceCount += (match.match(/&nbsp;/gi) || []).length;
      spaceCount += (match.match(/\u00A0/g) || []).length;
      spaceCount += (match.match(/&#160;/gi) || []).length;
      spaceCount += (match.match(/&#xa0;/gi) || []).length;
      const remainingMatch = match.replace(/(&nbsp;|\u00A0|&#160;|&#xa0;)/gi, '');
      spaceCount += remainingMatch.length;
      return '\u2003'.repeat(spaceCount);
    });
  },
});

// Test with your example
const testHTML = `<div>&nbsp; * inner</div>
<div>&nbsp; &nbsp; * more inner</div>
<div>&nbsp; &nbsp; &nbsp; * even more</div>`;

console.log('Original HTML:');
console.log(testHTML);

console.log('\nTurndown result:');
const turndownResult = turndownService.turndown(testHTML);
console.log('Raw:', JSON.stringify(turndownResult));

console.log('\nAfter converting em spaces to regular spaces:');
const finalMarkdown = turndownResult.replace(/\u2003/g, ' ');
console.log('Markdown:', JSON.stringify(finalMarkdown));

console.log('\nMarked result:');
const htmlResult = marked(finalMarkdown);
console.log(htmlResult);
